<?php
require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Crea un client di test se non esiste
$client = App\Models\Client::first();
if (!$client) {
    $client = App\Models\Client::create([
        'nome' => 'Mario',
        'cognome' => 'Rossi',
        'email' => '<EMAIL>',
        'telefono' => '123456789',
        'indirizzo' => 'Via Test 123',
        'tipo' => 'Persona Fisica'
    ]);
    echo "Creato client di test: " . $client->full_name . "\n";
}

// Crea un progetto di test
$project = App\Models\Project::create([
    'client_id' => $client->id,
    'titolo' => 'Progetto di Test per Cancellazione',
    'tipologia_lavoro' => 'Sviluppo sito web',
    'tipo_progetto' => 'One-shot',
    'data_scadenza' => now()->addDays(30),
    'compenso' => 1500.00,
    'stato' => 'In Corso',
    'descrizione' => 'Progetto creato per testare la cancellazione a cascata dei task'
]);

echo "Creato progetto di test: " . $project->titolo . "\n";

// Crea alcuni task collegati al progetto
$user = App\Models\User::first();
if (!$user) {
    echo "Errore: nessun utente trovato nel database\n";
    exit(1);
}

for ($i = 1; $i <= 3; $i++) {
    App\Models\Task::create([
        'project_id' => $project->id,
        'user_id' => $user->id,
        'titolo' => "Task di test #{$i}",
        'descrizione' => "Descrizione del task di test numero {$i}",
        'status' => 'Assegnato',
        'data_scadenza' => now()->addDays($i * 7)
    ]);
}

echo "Creati 3 task di test per il progetto\n";
echo "Ora puoi testare la cancellazione del progetto all'indirizzo: http://127.0.0.1:8000/projects\n";