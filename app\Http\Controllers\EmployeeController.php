<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

class EmployeeController extends Controller
{


    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Employee::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('nome', 'like', "%{$search}%")
                  ->orWhere('cognome', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('ruolo', 'like', "%{$search}%");
            });
        }

        // Filter by role
        if ($request->filled('ruolo')) {
            $query->where('ruolo', $request->ruolo);
        }

        $employees = $query->paginate(10)->withQueryString();

        return view('employees.index', compact('employees'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('employees.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'nome' => 'required|string|max:255',
            'cognome' => 'required|string|max:255',
            'ruolo' => 'required|in:Fotografo,Editor Video,Programmatore Web,Social Media Manager',
            'telefono' => 'required|string|max:20',
            'email' => 'required|email|unique:employees,email|unique:users,email',
            'foto' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'role' => 'required|in:Administrator,Dipendente',
        ]);

        // Generate secure password
        $password = $this->generateSecurePassword();
        $hashedPassword = Hash::make($password);
        $validated['password'] = $hashedPassword;

        // Handle photo upload
        $fotoPath = null;
        if ($request->hasFile('foto')) {
            $fotoPath = $request->file('foto')->store('employees', 'public');

            $validated['foto'] = $fotoPath;
        }

        // Use database transaction to ensure both records are created or none
        DB::transaction(function () use ($validated, $hashedPassword, $fotoPath) {
            // Create employee record
            $employee = Employee::create($validated);

            // Create corresponding user record for authentication
            User::create([
                'name' => $validated['nome'] . ' ' . $validated['cognome'],
                'email' => $validated['email'],
                'password' => $hashedPassword,
                'role' => $validated['role'],
                'foto' => $fotoPath,
            ]);
        });

        return redirect()->route('employees.index')
            ->with('success', "Dipendente creato con successo. Password generata: {$password}");
    }

    /**
     * Display the specified resource.
     */
    public function show(Employee $employee)
    {
        return view('employees.show', compact('employee'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Employee $employee)
    {
        return view('employees.edit', compact('employee'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Employee $employee)
    {
        $validated = $request->validate([
            'nome' => 'required|string|max:255',
            'cognome' => 'required|string|max:255',
            'ruolo' => 'required|in:Fotografo,Editor Video,Programmatore Web,Social Media Manager',
            'telefono' => 'required|string|max:20',
            'email' => [
                'required',
                'email',
                Rule::unique('employees')->ignore($employee->id),
                Rule::unique('users')->ignore($employee->email, 'email')
            ],
            'foto' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'role' => 'required|in:Administrator,Dipendente',
        ]);

        // Handle photo upload
        $fotoPath = $employee->foto; // Keep existing photo path by default
        if ($request->hasFile('foto')) {
            // Delete old photo if exists
            if ($employee->foto) {
                Storage::disk('public')->delete($employee->foto);
            }
            $fotoPath = $request->file('foto')->store('employees', 'public');
            $validated['foto'] = $fotoPath;
        }

        // Use database transaction to ensure both records are updated consistently
        DB::transaction(function () use ($validated, $employee, $fotoPath) {
            // Find corresponding user record BEFORE updating employee
            $user = User::where('email', $employee->email)->first();

            // Update employee record
            $employee->update($validated);

            // Update corresponding user record
            if ($user) {
                $user->update([
                    'name' => $validated['nome'] . ' ' . $validated['cognome'],
                    'email' => $validated['email'],
                    'role' => $validated['role'],
                    'foto' => $fotoPath,
                ]);
            }
        });

        return redirect()->route('employees.index')
            ->with('success', 'Dipendente aggiornato con successo.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Employee $employee)
    {
        // Use database transaction to ensure both records are deleted consistently
        DB::transaction(function () use ($employee) {
            // Delete photo if exists
            if ($employee->foto) {
                Storage::disk('public')->delete($employee->foto);
            }

            // Delete corresponding user record
            $user = User::where('email', $employee->email)->first();
            if ($user) {
                $user->delete();
            }

            // Delete employee record
            $employee->delete();
        });

        return redirect()->route('employees.index')
            ->with('success', 'Dipendente eliminato con successo.');
    }

    /**
     * Generate a secure password for new employees.
     */
    private function generateSecurePassword(): string
    {
        return Str::random(12);
    }

    /**
     * Generate a new password for an existing employee.
     */
    public function generatePassword(Employee $employee)
    {
        $newPassword = $this->generateSecurePassword();
        $hashedPassword = Hash::make($newPassword);

        // Use database transaction to ensure both records are updated consistently
        DB::transaction(function () use ($employee, $hashedPassword) {
            // Update employee password
            $employee->update(['password' => $hashedPassword]);

            // Update corresponding user password
            $user = User::where('email', $employee->email)->first();
            if ($user) {
                $user->update(['password' => $hashedPassword]);
            }
        });

        return redirect()->route('employees.index')
            ->with('success', "Nuova password generata per {$employee->full_name}: {$newPassword}");
    }
}
