@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0">Dettagli Progetto</h1>
            <p class="text-muted">Informazioni complete di "{{ $project->titolo }}"</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="{{ route('projects.edit', $project) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Modifica
            </a>
            <a href="{{ route('projects.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Torna alla lista
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informazioni Progetto</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Titolo</label>
                                <p class="fw-bold">{{ $project->titolo }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Cliente</label>
                                <p class="fw-bold">
                                    <a href="{{ route('clients.show', $project->client) }}" class="text-decoration-none">
                                        {{ $project->client->full_name }}
                                    </a>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Tipologia di Lavoro</label>
                                <p class="fw-bold">{{ $project->tipologia_lavoro }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Tipo Progetto</label>
                                <p>
                                    <span class="badge {{ $project->tipo_progetto == 'One-shot' ? 'bg-warning' : 'bg-info' }} fs-6">
                                        {{ $project->tipo_progetto }}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                @if($project->isOneShot())
                                    <label class="form-label text-muted">Data Scadenza</label>
                                    <p class="fw-bold">
                                        @if($project->data_scadenza)
                                            <i class="fas fa-calendar-alt"></i> {{ $project->data_scadenza->format('d/m/Y') }}
                                        @else
                                            <span class="text-muted">Non specificata</span>
                                        @endif
                                    </p>
                                @else
                                    <label class="form-label text-muted">Frequenza</label>
                                    <p class="fw-bold">
                                        <i class="fas fa-sync-alt"></i> {{ $project->frequenza ?? 'Non specificata' }}
                                    </p>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Stato</label>
                                <p>
                                    <span class="badge badge-{{ strtolower(str_replace(' ', '-', $project->stato)) }} fs-6">
                                        {{ $project->stato }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Compenso</label>
                                <p class="fw-bold fs-5 text-success">€ {{ number_format($project->compenso, 2) }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Data Creazione</label>
                                <p class="fw-bold">{{ $project->created_at->format('d/m/Y H:i') }}</p>
                            </div>
                        </div>
                    </div>
                    
                    @if($project->descrizione)
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Descrizione</label>
                                <p class="fw-bold">{{ $project->descrizione }}</p>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    @if($project->note)
                    <div class="row">
                        <div class="col-12">
                            <div class="mb-3">
                                <label class="form-label text-muted">Note Interne</label>
                                <p class="fw-bold">{{ $project->note }}</p>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    @if($project->updated_at != $project->created_at)
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Ultimo Aggiornamento</label>
                                <p class="fw-bold">{{ $project->updated_at->format('d/m/Y H:i') }}</p>
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Tasks Section -->
            <div class="card mt-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Tasks Collegati</h5>
                    @if(auth()->user()->isAdmin())
                        <a href="{{ route('tasks.create', ['project_id' => $project->id]) }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> Nuovo Task
                        </a>
                    @endif
                </div>
                <div class="card-body">
                    @if($project->tasks->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($project->tasks as $task)
                            <div class="list-group-item px-0">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">
                                            <a href="{{ route('tasks.show', $task) }}" class="text-decoration-none">
                                                {{ $task->titolo }}
                                            </a>
                                        </h6>
                                        @if($task->descrizione)
                                            <p class="mb-1 text-muted small">{{ Str::limit($task->descrizione, 80) }}</p>
                                        @endif
                                        <small class="text-muted">
                                            <i class="fas fa-user"></i> {{ $task->user->name }} •
                                            <i class="fas fa-calendar-alt"></i> {{ $task->data_scadenza->format('d/m/Y') }}
                                            @if($task->isOverdue())
                                                <span class="text-danger">
                                                    <i class="fas fa-exclamation-triangle"></i> Scaduto
                                                </span>
                                            @endif
                                        </small>
                                    </div>
                                    <div class="ms-3">
                                        <span class="badge {{ $task->getStatusBadgeClass() }}">
                                            {{ $task->status }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        <div class="mt-3 text-center">
                            <a href="{{ route('tasks.index', ['project_id' => $project->id]) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-tasks"></i> Vedi tutti i tasks
                            </a>
                        </div>
                    @else
                        <div class="text-center py-3">
                            <i class="fas fa-tasks fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Nessun task collegato a questo progetto</p>
                            @if(auth()->user()->isAdmin())
                                <a href="{{ route('tasks.create', ['project_id' => $project->id]) }}" class="btn btn-sm btn-primary mt-2">
                                    <i class="fas fa-plus"></i> Crea il primo task
                                </a>
                            @endif
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informazioni Cliente</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <h6>{{ $project->client->full_name }}</h6>
                        <span class="badge {{ $project->client->tipo == 'Persona Fisica' ? 'bg-info' : 'bg-success' }}">
                            {{ $project->client->tipo }}
                        </span>
                    </div>
                    
                    @if($project->client->email)
                    <p class="mb-2">
                        <i class="fas fa-envelope"></i> 
                        <a href="mailto:{{ $project->client->email }}">{{ $project->client->email }}</a>
                    </p>
                    @endif
                    
                    @if($project->client->telefono)
                    <p class="mb-2">
                        <i class="fas fa-phone"></i> 
                        <a href="tel:{{ $project->client->telefono }}">{{ $project->client->telefono }}</a>
                    </p>
                    @endif
                    
                    @if($project->client->indirizzo)
                    <p class="mb-2">
                        <i class="fas fa-map-marker-alt"></i> {{ $project->client->indirizzo }}
                    </p>
                    @endif
                    
                    <div class="mt-3">
                        <a href="{{ route('clients.show', $project->client) }}" class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-eye"></i> Vedi Cliente
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">Azioni</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($project->stato !== 'Completato')
                        <form action="{{ route('projects.update', $project) }}" method="POST">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="client_id" value="{{ $project->client_id }}">
                            <input type="hidden" name="titolo" value="{{ $project->titolo }}">
                            <input type="hidden" name="tipologia_lavoro" value="{{ $project->tipologia_lavoro }}">
                            <input type="hidden" name="tipo_progetto" value="{{ $project->tipo_progetto }}">
                            <input type="hidden" name="data_scadenza" value="{{ $project->data_scadenza?->format('Y-m-d') }}">
                            <input type="hidden" name="frequenza" value="{{ $project->frequenza }}">
                            <input type="hidden" name="compenso" value="{{ $project->compenso }}">
                            <input type="hidden" name="descrizione" value="{{ $project->descrizione }}">
                            <input type="hidden" name="note" value="{{ $project->note }}">
                            <input type="hidden" name="stato" value="Completato">
                            <button type="submit" class="btn btn-success btn-sm w-100">
                                <i class="fas fa-check"></i> Segna come Completato
                            </button>
                        </form>
                        @endif
                        
                        <form action="{{ route('projects.destroy', $project) }}" method="POST" class="delete-form">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm w-100 delete-project-btn" 
                                    data-project-title="{{ $project->titolo }}" 
                                    data-tasks-count="{{ $project->tasks->count() }}">
                                <i class="fas fa-trash"></i> Elimina Progetto
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle delete project confirmation
    document.querySelectorAll('.delete-project-btn').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const projectTitle = this.getAttribute('data-project-title');
            const tasksCount = parseInt(this.getAttribute('data-tasks-count'));
            const form = this.closest('.delete-form');
            
            let message = `Sei sicuro di voler eliminare il progetto "${projectTitle}"?`;
            
            if (tasksCount > 0) {
                message += `\n\n⚠️ ATTENZIONE: Questo progetto ha ${tasksCount} task collegati che verranno eliminati definitivamente insieme al progetto.`;
            }
            
            if (confirm(message)) {
                form.submit();
            }
        });
    });
});
</script>
@endpush
@endsection
