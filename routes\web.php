<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\TaskController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('login');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Task Management - accessible by both admin and employees
    Route::resource('tasks', TaskController::class);

    // Admin-only routes
    Route::middleware('admin')->group(function () {
        // Employee Management
        Route::resource('employees', EmployeeController::class);
        Route::post('employees/{employee}/generate-password', [EmployeeController::class, 'generatePassword'])
            ->name('employees.generate-password');

        // Client Management
        Route::resource('clients', ClientController::class);

        // Project Management
        Route::resource('projects', ProjectController::class);
    });
});

require __DIR__.'/auth.php';
